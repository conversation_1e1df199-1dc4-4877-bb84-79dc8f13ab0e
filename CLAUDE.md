# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Home Assistant dashboard configuration project that defines a comprehensive smart home UI for a Czech household. The entire configuration is contained in `dashboard.yaml`.

## Architecture

### Dashboard Structure
The dashboard consists of multiple views (tabs):
- **Klima** - Climate control for air conditioning units
- **Home** - Solar power monitoring and energy flow visualization
- **Surveillance** - Security camera feeds
- **Zahrada** - Garden and pool controls
- **Topení** - Central heating system controls
- **History** - Historical data graphs
- **Domácnost** - Household appliance controls
- **Analytika** - Energy and air quality analytics

### Custom Cards Used
The dashboard relies on several custom Home Assistant cards:
- `power-flow-card-plus` - Energy flow visualization
- `mini-graph-card` - Compact graphs for sensor data
- `sankey-chart` - Energy distribution visualization
- `advanced-camera-card` - Enhanced camera controls
- `vacuum-card` - Robot vacuum controls
- `state-content` - (Currently causing errors)

## Development Workflow

### Making Changes
1. Edit `dashboard.yaml` directly
2. Save changes and reload the dashboard in Home Assistant UI
3. Check for YAML syntax errors in Home Assistant logs

### Testing Changes
- Changes are tested live in the Home Assistant environment
- Use Home Assistant's configuration validation: Settings → System → Check Configuration
- Monitor browser console for JavaScript errors when testing UI changes

### Common Tasks

To validate YAML syntax locally:
```bash
# Install yamllint if needed
pip install yamllint

# Check syntax
yamllint dashboard.yaml
```

To reload dashboard without restarting Home Assistant:
- Navigate to Developer Tools → YAML → Lovelace Dashboards → Reload

## Known Issues

The `errors/` directory contains screenshots showing "Unknown type encountered: state-content" errors. This indicates:
- The `state-content` card type is either deprecated or requires installation
- Affected cards need to be replaced with supported alternatives
- Check Home Assistant version compatibility with custom cards

## Entity Naming Convention

Entities follow Czech naming with descriptive suffixes:
- Climate entities: `climate.klimatizace_[location]`
- Sensors: `sensor.[device]_[measurement]`
- Switches: `switch.[device]_[function]`
- Binary sensors: `binary_sensor.[device]_[state]`

## Important Considerations

- All text displayed to users should be in Czech
- Maintain consistent card layouts within sections
- Group related controls logically
- Consider mobile responsiveness when adding new cards
- Monitor performance impact of complex cards (especially graphs and energy flows)