views:
  - type: sections
    max_columns: 4
    icon: mdi:home-thermometer
    title: Klima
    path: klima
    sections:
      - type: grid
        cards:
          - type: thermostat
            entity: climate.klimatizace_pokoj_levy
            show_current_as_primary: false
            name: Klimatizace Adélka
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: tile
            entity: switch.klimatizace_pokoj_levy_jet_mode
            name: Jet mode
            vertical: true
          - type: tile
            entity: switch.klimatizace_pokoj_levy_ionizer
            name: Ionizer
            vertical: true
            tap_action:
              action: toggle
          - type: tile
            entity: sensor.klimatizace_pokoj_levy_filter_remaining_life
            name: Filtr
          - type: tile
            entity: sensor.klimatizace_pokoj_levy_sleep_time
            name: Sleep
      - type: grid
        cards:
          - type: thermostat
            entity: climate.klimatizace_pokoj_stred
            name: Klimatizace Ložnice
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: tile
            entity: switch.klimatizace_pokoj_stred_jet_mode
            vertical: true
            name: Jet Mode
          - type: tile
            entity: switch.klimatizace_pokoj_stred_ionizer
            name: Ionizer
            vertical: true
            tap_action:
              action: toggle
          - type: tile
            entity: sensor.klimatizace_pokoj_stred_filter_remaining_life
            name: Filtr
          - type: tile
            entity: sensor.klimatizace_pokoj_stred_sleep_time
            name: Sleep
      - type: grid
        cards:
          - type: thermostat
            entity: climate.klimatizace_pracovna
            name: Klimatizace Pracovna
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: tile
            entity: switch.klimatizace_pracovna_jet_mode
            name: Jet Mode
            show_entity_picture: false
            vertical: true
            hide_state: false
          - type: tile
            entity: switch.klimatizace_pracovna_ionizer
            name: Ionizer
            vertical: true
            tap_action:
              action: toggle
          - type: tile
            entity: sensor.klimatizace_pracovna_sleep_time
            name: Sleep
            vertical: false
            hide_state: false
            show_entity_picture: false
          - type: tile
            entity: sensor.klimatizace_pracovna_filter_remaining_life
            vertical: false
            name: Filtr
            show_entity_picture: false
            hide_state: false
      - type: grid
        cards: []
  - title: Home
    type: sidebar
    icon: mdi:solar-power-variant
    cards:
      - type: custom:power-flow-card-plus
        title: Aktuální stav FVE
        entities:
          grid:
            entity:
              consumption: sensor.energy_buy
              production: sensor.energy_sell
            display_state: two_way
            color_circle: true
          solar:
            entity: sensor.pv_power
          battery:
            entity: sensor.battery_power
            state_of_charge: sensor.battery_state_of_charge
            display_state: one_way
            color_circle: true
          home:
            color_icon: true
            entity: sensor.house_consumption
            override_state: true
        watt_threshold: 10000
        clickable_entities: true
        display_zero_lines:
          mode: show
          transparency: 50
          grey_color:
            - 189
            - 189
            - 189
        view_layout:
          position: main
        use_new_flow_rate_model: true
      - type: horizontal-stack
        cards:
          - type: gauge
            entity: sensor.battery_state_of_charge
            name: Stav nabití
            min: 0
            max: 100
            unit: '%'
            severity:
              green: 70
              yellow: 40
              red: 20
          - type: entities
            title: Nastavení baterie
            show_header_toggle: false
            entities:
              - entity: number.goodwe_depth_of_discharge_on_grid
                name: Max. vybití (on-grid)
              - entity: select.goodwe_inverter_operation_mode
                name: Režim
              - entity: number.goodwe_grid_export_limit
                name: Limit přetoku
      - type: vertical-stack
        title: Statistiky
        view_layout:
          position: main
        cards:
          - type: energy-date-selection
          - type: horizontal-stack
            cards:
              - type: energy-self-sufficiency-gauge
              - type: energy-solar-consumed-gauge
              - type: energy-grid-neutrality-gauge
          - type: energy-distribution
          - type: energy-usage-graph
          - type: energy-solar-graph
      - type: horizontal-stack
        cards:
          - type: history-graph
            title: Produkce FVE
            entities:
              - entity: sensor.pv1_power
                name: Panely východ
              - entity: sensor.pv2_power
                name: Panely západ
              - entity: sensor.pv_power
                name: Panely celkem
            hours_to_show: 24
          - type: history-graph
            title: Teploty
            hours_to_show: 24
            entities:
              - entity: sensor.battery_temperature
                name: Teplota baterie
              - entity: sensor.inverter_temperature_air
                name: Teplota střídače (vzduch)
              - entity: sensor.inverter_temperature_radiator
                name: Teplota střídače (chladič)
      - type: custom:sankey-chart
        title: Tok Energie
        unit_prefix: k
        round: 2
        show_names: true
        energy_date_selection: true
        show_icons: false
        sections:
          - entities:
              - type: entity
                entity_id: sensor.total_pv_generation
                name: Solar
                color: '#ff9800'
                children:
                  - sensor.total_battery_charge
                  - sensor.energy_sell_daily
                  - sensor.house_consumption_daily
              - type: entity
                entity_id: sensor.energy_buy_daily
                name: Grid
                color: '#488fc2'
                children:
                  - sensor.house_consumption_daily
                  - sensor.total_battery_charge
              - type: entity
                entity_id: sensor.total_battery_discharge
                name: Baterie
                color: '#4db6ac'
                children:
                  - sensor.house_consumption_daily
                  - sensor.energy_sell_daily
          - entities:
              - type: entity
                entity_id: sensor.house_consumption_daily
                name: Dům
                color: lightblue
              - type: entity
                entity_id: sensor.total_battery_charge
                name: Baterie
                color: '#f06292'
                children: []
              - type: entity
                entity_id: sensor.energy_sell_daily
                name: Přetok
                color: '#8353d1'
                children: []
  - title: surveillance
    cards: []
    icon: mdi:camera
    type: sections
    max_columns: 2
    subview: false
    sections:
      - type: grid
        cards:
          - show_state: true
            show_name: true
            camera_view: auto
            type: picture-entity
            image: https://demo.home-assistant.io/stub_config/bedroom.png
            entity: camera.ulice
            camera_image: camera.ulice
          - type: custom:advanced-camera-card
            cameras:
              - camera_entity: camera.ulice_frigate
            view:
              default: clips
            menu:
              style: outside
              buttons:
                camera_ui:
                  enabled: false
                snapshots:
                  enabled: false
                live:
                  enabled: false
                substreams:
                  enabled: false
                cameras:
                  enabled: false
                recordings:
                  enabled: true
                iris:
                  enabled: false
            media_viewer:
              draggable: true
              zoomable: true
              snapshot_click_plays_clip: true
            timeline: {}
            dimensions:
              height: 80vh
      - type: grid
        cards:
          - show_state: true
            show_name: true
            camera_view: auto
            type: picture-entity
            image: https://demo.home-assistant.io/stub_config/bedroom.png
            entity: camera.reolink_zahrada_fluent
            camera_image: camera.zahrada
            name: Zahrada
          - type: custom:advanced-camera-card
            cameras:
              - camera_entity: camera.zahrada_bouda
            view:
              default: clips
            menu:
              style: outside
              buttons:
                camera_ui:
                  enabled: false
                snapshots:
                  enabled: false
                live:
                  enabled: false
                substreams:
                  enabled: false
                cameras:
                  enabled: false
                recordings:
                  enabled: true
                iris:
                  enabled: false
            media_viewer:
              draggable: true
              zoomable: true
              snapshot_click_plays_clip: true
            timeline: {}
            dimensions:
              height: 80vh
      - type: grid
        cards:
          - type: heading
            heading: New section
          - show_state: true
            show_name: true
            camera_view: auto
            type: picture-entity
            image: https://demo.home-assistant.io/stub_config/bedroom.png
            entity: camera.zahrada
            camera_image: camera.zahrada
          - type: custom:advanced-camera-card
            cameras:
              - camera_entity: camera.zahrada_frigate
            view:
              default: clips
            menu:
              style: outside
              buttons:
                camera_ui:
                  enabled: false
                snapshots:
                  enabled: false
                live:
                  enabled: false
                substreams:
                  enabled: false
                cameras:
                  enabled: false
                recordings:
                  enabled: true
                iris:
                  enabled: false
            media_viewer:
              draggable: true
              zoomable: true
              snapshot_click_plays_clip: true
            timeline: {}
            dimensions:
              height: 80vh
  - type: sections
    max_columns: 3
    title: Zahrada
    path: zahrada
    icon: mdi:forest-outline
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Bouda - Ovládání
            heading_style: title
          - type: vertical-stack
            cards:
              - type: horizontal-stack
                cards:
                  - type: tile
                    entity: switch.sonoff_4ch01_1
                    name: Zásuvky uvnitř
                    icon: mdi:power-socket-eu
                    vertical: true
                    color: blue
                  - type: tile
                    entity: switch.sonoff_4ch01_1_sonoff_4ch01_2
                    name: Světla
                    icon: mdi:lightbulb
                    vertical: true
                    color: yellow
                  - type: tile
                    entity: switch.sonoff_4ch01_1_sonoff_4ch01_4
                    name: Zásuvky venku
                    icon: mdi:power-socket-eu
                    vertical: true
                    color: green
              - type: tile
                entity: binary_sensor.0x00158d000315fac9_contact
                name: Dveře bouda
                icon: mdi:door
                color: red
      - type: grid
        cards:
          - type: heading
            heading: Bazén - Ovládání
            heading_style: title
          - type: vertical-stack
            cards:
              - type: horizontal-stack
                cards:
                  - type: tile
                    entity: switch.sonoff_4ch01_1_sonoff_4ch01_3
                    name: Filtrace bazénu
                    icon: mdi:pump
                    vertical: true
                    color: blue
                    features_position: bottom
                  - type: tile
                    entity: input_boolean.solar_pool
                    name: Solární ohřev
                    icon: mdi:solar-panel
                    vertical: true
                    color: orange
                    features_position: bottom
              - type: horizontal-stack
                cards:
                  - type: gauge
                    entity: sensor.gw1100a_temperature_3
                    name: Teplota vody
                    unit: °C
                    min: 0
                    max: 40
                    needle: true
                    severity:
                      green: 22
                      yellow: 28
                      red: 32
                  - type: entity
                    entity: sensor.bazenove_cerpadlo_dnes_celkem
                    name: Spotřeba dnes
                    icon: mdi:flash
      - type: grid
        cards:
          - type: heading
            heading: Zahradní podmínky
            heading_style: title
          - type: vertical-stack
            cards:
              - type: horizontal-stack
                cards:
                  - type: gauge
                    entity: sensor.gw1100a_temperature_1
                    name: Venkovní teplota
                    unit: °C
                    min: -10
                    max: 40
                    needle: true
                    severity:
                      green: 15
                      yellow: 25
                      red: 35
                  - type: gauge
                    entity: sensor.gw1100a_humidity_1
                    name: Vlhkost vzduchu
                    unit: '%'
                    min: 0
                    max: 100
                    needle: true
                    severity:
                      green: 40
                      yellow: 30
                      red: 0
              - type: custom:horizon-card
                entity: sun.sun
                name: Pozice slunce
              - show_current: true
                show_forecast: true
                type: weather-forecast
                entity: weather.openweathermap
                forecast_type: daily
      - type: grid
        cards:
          - type: heading
            heading: Historie & Grafy
            heading_style: title
          - type: vertical-stack
            cards:
              - type: custom:mini-graph-card
                name: Teploty - 48h
                entities:
                  - entity: sensor.gw1100a_temperature_1
                    name: Venku
                    color: '#03a9f4'
                  - entity: sensor.gw1100a_temperature_3
                    name: Bazén
                    color: '#00bcd4'
                hours_to_show: 48
                points_per_hour: 2
                line_width: 2
                show:
                  labels: true
                  average: true
                  extrema: true
              - type: custom:mini-graph-card
                name: Provoz filtrace
                entities:
                  - entity: switch.sonoff_4ch01_1_sonoff_4ch01_3
                    name: Filtrace
                    color: '#4caf50'
                    show_state: true
                    aggregate_func: max
                    y_axis: secondary
                  - entity: sensor.bazenove_cerpadlo_dnes_celkem
                    name: Spotřeba
                    color: '#ff9800'
                    show_state: true
                hours_to_show: 24
                points_per_hour: 4
                show:
                  graph: bar
                  labels: true
      - type: grid
        cards:
          - type: heading
            heading: Rychlé akce
            heading_style: title
          - type: vertical-stack
            cards:
              - show_name: true
                show_icon: true
                type: button
                name: Zapnout vše v boudě
                icon: mdi:power
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.bouda_all_on
              - type: button
                name: Vypnout vše v boudě
                icon: mdi:power-off
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.bouda_all_off
              - show_name: true
                show_icon: true
                type: button
                name: Noční režim zahrady
                icon: mdi:weather-night
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.garden_night_mode
  - title: Topení
    icon: mdi:heat-wave
    type: sections
    max_columns: 4
    subview: false
    sections:
      - type: grid
        cards:
          - type: thermostat
            entity: climate.obyvaci_pokoj
            features:
              - type: climate-hvac-modes
            show_current_as_primary: false
            name: Obývací pokoj
          - type: thermostat
            entity: climate.pokoj_dole
            features:
              - type: climate-hvac-modes
            name: Pokoj dole
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.koupelna_dole
            name: Koupelna dole
          - type: thermostat
            entity: climate.loznice
            features:
              - type: climate-hvac-modes
            show_current_as_primary: false
            name: Ložnice
      - type: grid
        cards:
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.koupelna_nahore
            name: Koupelna nahoře
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.0x84fd27fffea36224
            name: Chodba
          - type: thermostat
            entity: climate.pokoj_levy
            features:
              - type: climate-hvac-modes
            name: Pokoj levý
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.pokoj_pravy
            name: Pokoj pravý
  - title: History
    path: history
    icon: mdi:chart-histogram
    type: sections
    max_columns: 3
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Klimatizace a teploty
            heading_style: title
      - type: grid
        cards:
          - type: history-graph
            entities:
              - entity: climate.klimatizace_pokoj_levy
              - entity: sensor.teplota_pokoj_levy
              - entity: sensor.req_teplota_pokoj_levy
            title: Pokoj Levý (Adélka)
            hours_to_show: 24
          - type: history-graph
            entities:
              - entity: climate.klimatizace_pokoj_stred_2
              - entity: sensor.teplota_loznice
              - entity: sensor.req_teplota_loznice
            title: Ložnice
            hours_to_show: 24
          - type: history-graph
            entities:
              - entity: climate.klimatizace_pracovna
              - entity: sensor.teplota_pokoj_pravy
              - entity: sensor.req_teplota_pokoj_pravy
            title: Pracovna
            hours_to_show: 24
      - type: grid
        cards:
          - type: history-graph
            entities:
              - entity: sensor.teplota_obyvaci_pokoj
              - entity: sensor.req_teplota_obyvaci_pokoj
            title: Obývací pokoj
            hours_to_show: 24
          - type: history-graph
            entities:
              - entity: sensor.teplota_pokoj_dole
              - entity: sensor.req_teplota_pokoj_dole
            title: Pokoj dole
            hours_to_show: 24
          - type: history-graph
            entities:
              - entity: sensor.teplota_koupelna_nahore
              - entity: sensor.req_teplota_koupelna_nahore
            title: Koupelna nahoře
            hours_to_show: 24
      - type: grid
        cards:
          - type: heading
            heading: Venkovní podmínky
            heading_style: title
      - type: grid
        cards:
          - type: history-graph
            entities:
              - entity: >-
                  sensor.netatmo_moje_domacnost_weather_station_outdoor_module_temperature
              - entity: sensor.gw1100a_temperature_1
            title: Venkovní teplota
            hours_to_show: 48
          - type: history-graph
            entities:
              - entity: >-
                  sensor.netatmo_moje_domacnost_weather_station_outdoor_module_humidity
              - entity: sensor.gw1100a_humidity_1
            title: Venkovní vlhkost
            hours_to_show: 48
          - type: history-graph
            entities:
              - entity: sensor.gw1100a_temperature_3
              - entity: sensor.wifi_teplomer
            title: Teplota bazénu
            hours_to_show: 72
      - type: grid
        cards:
          - type: heading
            heading: Kvalita vzduchu
            heading_style: title
      - type: grid
        cards:
          - type: history-graph
            entities:
              - entity: sensor.netatmo_moje_domacnost_weather_station_co2
            title: CO₂ - Obývací pokoj
            hours_to_show: 24
          - type: history-graph
            entities:
              - entity: >-
                  sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2
            title: CO₂ - Ložnice
            hours_to_show: 24
          - type: history-graph
            entities:
              - entity: >-
                  sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2
            title: CO₂ - Pracovna
            hours_to_show: 24
  - type: sections
    path: ''
    max_columns: 4
    icon: mdi:account-group
    sections:
      - type: grid
        cards:
          - type: heading
            heading_style: subtitle
          - type: vertical-stack
            cards:
              - type: markdown
                content: '## Climate Actions'
              - type: horizontal-stack
                cards:
                  - show_name: true
                    show_icon: true
                    type: button
                    name: Evening Mode
                    icon: mdi:moon-waning-crescent
                    tap_action:
                      action: call-service
                      service: script.climate_evening
                  - type: button
                    name: Morning Mode
                    icon: mdi:white-balance-sunny
                    tap_action:
                      action: call-service
                      service: script.climate_morning
                  - type: button
                    name: Toggle Boiler
                    entity: switch.kotel
                    icon: mdi:radiator
                    tap_action:
                      action: toggle
                  - type: button
                    name: Heat Season
                    entity: input_boolean.topna_sezona
                    icon: mdi:snowflake
                    tap_action:
                      action: toggle
              - type: markdown
                content: '## Scheduled Settings'
              - type: entities
                entities:
                  - entity: input_boolean.zapnout_rano_topeni
                    name: Morning Heating On?
                    icon: mdi:weather-sunny-alert
                  - entity: input_boolean.vypnout_vecer_topeni
                    name: Evening Heating Off?
                    icon: mdi:moon-waning-crescent
              - type: markdown
                content: '## Temperatures – Morning'
              - type: entities
                entities:
                  - input_number.morning_loznice
                  - input_number.morning_pokoj_vlevo
                  - input_number.morning_pokoj_dole
                  - input_number.morning_obyvak
              - type: markdown
                content: '## Temperatures – Evening & Sleep'
              - type: entities
                entities:
                  - input_number.sleep_loznice
                  - input_number.sleep_pokoj_vlevo
                  - input_number.sleep_pokoj_dole
                  - input_number.sleep_obyvak
              - type: markdown
                content: '## Bathroom Heating'
              - type: entities
                entities:
                  - input_number.koupani_koupelna_nahore
                  - input_number.koupani_koupelna_dole
                  - input_boolean.zapnout_koupelna_nahore_topeni
                  - input_boolean.zapnout_koupelna_dole_topeni
                  - input_datetime.start_heat_koupelna_nahore
                  - input_datetime.start_heat_koupelna_dole
  - type: sections
    max_columns: 4
    title: Domácnost
    path: domacnost
    icon: mdi:home-account
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Praní a sušení
            heading_style: title
          - type: vertical-stack
            cards:
              - type: horizontal-stack
                cards:
                  - type: tile
                    entity: binary_sensor.pracka_pere_status
                    name: Pračka
                    icon: mdi:washing-machine
                    vertical: false
                  - type: tile
                    entity: sensor.doba_prani_pracky
                    name: Doba praní
                    icon: mdi:timer-outline
                  - type: gauge
                    entity: sensor.shellymini_koupelna_nahore_01_power
                    name: Příkon
                    unit: W
                    min: 0
                    max: 2500
                    needle: true
                    severity:
                      green: 0
                      yellow: 1000
                      red: 2000
              - type: horizontal-stack
                cards:
                  - type: tile
                    entity: binary_sensor.susicka_susi_status
                    name: Sušička
                    icon: mdi:tumble-dryer
                    vertical: false
                  - type: tile
                    entity: sensor.doba_suseni_susicky
                    name: Doba sušení
                    icon: mdi:timer-outline
                  - type: gauge
                    entity: sensor.shellymini_koupelna_nahore_02_power
                    name: Příkon
                    unit: W
                    min: 0
                    max: 2500
                    needle: true
                    severity:
                      green: 0
                      yellow: 1000
                      red: 2000
      - type: grid
        cards:
          - type: heading
            heading: Chladnička
            heading_style: title
          - type: vertical-stack
            cards:
              - type: horizontal-stack
                cards:
                  - type: tile
                    entity: binary_sensor.chladnicka_door
                    name: Dveře
                    icon: mdi:fridge-variant
                    color: red
                  - type: tile
                    entity: binary_sensor.chladnicka_eco_friendly
                    name: Eco režim
                    icon: mdi:leaf
                    color: green
              - type: horizontal-stack
                cards:
                  - type: tile
                    entity: switch.chladnicka_express_cool
                    name: Rychlé chlazení
                    icon: mdi:snowflake
                    color: blue
                  - type: tile
                    entity: switch.chladnicka_express_mode_2
                    name: Rychlé mražení
                    icon: mdi:snowflake-melt
                    color: cyan
              - type: horizontal-stack
                cards:
                  - type: tile
                    entity: number.chladnicka_fridge_temperature
                    name: Chladnička
                    icon: mdi:thermometer
                    color: blue
                    features:
                      - type: numeric-input
                        style: slider
                  - type: tile
                    entity: number.chladnicka_freezer_temperature
                    name: Mrazák
                    icon: mdi:thermometer
                    color: cyan
                    features:
                      - type: numeric-input
                        style: slider
      - type: grid
        cards:
          - type: heading
            heading: Kvalita vzduchu
            heading_style: title
          - type: vertical-stack
            cards:
              - type: heading
                heading: Obývací pokoj
                heading_style: subtitle
              - type: horizontal-stack
                cards:
                  - type: gauge
                    entity: sensor.netatmo_moje_domacnost_weather_station_co2
                    name: CO₂
                    unit: ppm
                    min: 0
                    max: 3000
                    needle: true
                    severity:
                      green: 0
                      yellow: 1000
                      red: 2000
                  - type: gauge
                    entity: sensor.netatmo_moje_domacnost_weather_station_humidity
                    name: Vlhkost
                    unit: '%'
                    min: 0
                    max: 100
                    needle: true
                    severity:
                      red: 0
                      yellow: 30
                      green: 40
                  - type: gauge
                    entity: sensor.netatmo_moje_domacnost_weather_station_noise
                    name: Hluk
                    unit: dB
                    min: 0
                    max: 100
                    needle: true
                    severity:
                      green: 0
                      yellow: 50
                      red: 70
              - type: heading
                heading: Ložnice
                heading_style: subtitle
              - type: horizontal-stack
                cards:
                  - type: gauge
                    entity: >-
                      sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2
                    name: CO₂
                    unit: ppm
                    min: 0
                    max: 3000
                    needle: true
                    severity:
                      green: 0
                      yellow: 1000
                      red: 2000
                  - type: gauge
                    entity: >-
                      sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_temperature
                    name: Teplota
                    unit: °C
                    min: 10
                    max: 35
                    needle: true
                    severity:
                      blue: 10
                      green: 20
                      yellow: 25
                      red: 30
                  - type: gauge
                    entity: >-
                      sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_humidity
                    name: Vlhkost
                    unit: '%'
                    min: 0
                    max: 100
                    needle: true
                    severity:
                      red: 0
                      yellow: 30
                      green: 40
              - type: heading
                heading: Pracovna
                heading_style: subtitle
              - type: horizontal-stack
                cards:
                  - type: gauge
                    entity: >-
                      sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2
                    name: CO₂
                    unit: ppm
                    min: 0
                    max: 3000
                    needle: true
                    severity:
                      green: 0
                      yellow: 1000
                      red: 2000
                  - type: gauge
                    entity: >-
                      sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_temperature
                    name: Teplota
                    unit: °C
                    min: 10
                    max: 35
                    needle: true
                    severity:
                      blue: 10
                      green: 20
                      yellow: 25
                      red: 30
                  - type: gauge
                    entity: >-
                      sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_humidity
                    name: Vlhkost
                    unit: '%'
                    min: 0
                    max: 100
                    needle: true
                    severity:
                      red: 0
                      yellow: 30
                      green: 40
      - type: grid
        cards:
          - type: heading
            heading: Zábava a doprava
            heading_style: title
          - type: vertical-stack
            cards:
              - type: media-control
                entity: media_player.lg_webos_tv_oled55c11lb
                name: LG OLED TV
              - type: horizontal-stack
                cards:
                  - type: entity
                    entity: sensor.cesta_do_cloudfield_z_domova
                    name: Do práce
                    icon: mdi:office-building
                  - type: entity
                    entity: sensor.cesta_na_parkoviste
                    name: Parkoviště
                    icon: mdi:parking
      - type: grid
        cards:
          - type: heading
            heading: Spotřeba energie
            heading_style: title
          - type: vertical-stack
            cards:
              - type: statistics-graph
                entities:
                  - sensor.shelly_klimatizace_switch_0_energy
                stat_types:
                  - sum
                period: day
                days_to_show: 7
                title: Spotřeba klimatizace (7 dní)
              - type: horizontal-stack
                cards:
                  - type: tile
                    entity: sensor.shelly_klimatizace_switch_0_power
                    name: Aktuální příkon
                    icon: mdi:flash
                    color: yellow
                  - type: tile
                    entity: switch.shelly_klimatizace_switch_0
                    name: Klimatizace
                    icon: mdi:air-conditioner
      - type: grid
        cards:
          - type: heading
            heading: Vysavače
            heading_style: title
          - type: custom:vacuum-card
            entity: vacuum.xiaomi_robot_vacuum_x10
            map: camera.xiaomi_robot_vacuum_x10_map
          - type: custom:vacuum-card
            entity: vacuum.vysavac_horni_patro
  - type: sections
    max_columns: 4
    title: Analytika
    path: analytics
    icon: mdi:chart-line
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Energetická analýza
            heading_style: title
          - type: vertical-stack
            cards:
              - type: custom:mini-graph-card
                name: Solární výroba vs. spotřeba
                entities:
                  - entity: sensor.pv_power
                    name: Výroba
                    color: '#ff9800'
                  - entity: sensor.house_consumption
                    name: Spotřeba
                    color: '#03a9f4'
                hours_to_show: 48
                points_per_hour: 4
                line_width: 2
                show:
                  labels: true
                  average: true
                  extrema: true
      - type: grid
        cards:
          - type: heading
            heading: Kvalita vzduchu - trendy
            heading_style: title
          - type: vertical-stack
            cards:
              - type: custom:mini-graph-card
                name: Úrovně CO₂
                entities:
                  - entity: sensor.netatmo_moje_domacnost_weather_station_co2
                    name: Obývák
                  - entity: >-
                      sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2
                    name: Ložnice
                  - entity: >-
                      sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2
                    name: Pracovna
                hours_to_show: 48
                points_per_hour: 4
                color_thresholds:
                  - value: 1000
                    color: '#ffeb3b'
                  - value: 2000
                    color: '#f44336'
                show:
                  labels: true
                  average: true
              - type: custom:mini-graph-card
                name: Vlhkost
                entities:
                  - entity: sensor.netatmo_moje_domacnost_weather_station_humidity
                    name: Obývák
                  - entity: >-
                      sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_humidity
                    name: Ložnice
                  - entity: >-
                      sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_humidity
                    name: Pracovna
                hours_to_show: 168
                aggregate_func: avg
                group_by: hour
                show:
                  graph: bar
      - type: grid
        cards:
          - type: heading
            heading: New section
  - title: Klima & Topení
    icon: mdi:thermometer-lines
    type: sections
    max_columns: 4
    path: climate-control
    subview: false
    sections:
      - type: grid
        cards:
          - type: thermostat
            entity: climate.klimatizace_pokoj_levy
            show_current_as_primary: false
            name: Klimatizace Adélka
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: tile
            entity: switch.klimatizace_pokoj_levy_jet_mode
            name: Jet mode
            vertical: true
          - type: tile
            entity: switch.klimatizace_pokoj_levy_ionizer
            name: Ionizer
            vertical: true
            tap_action:
              action: toggle
          - type: tile
            entity: sensor.klimatizace_pokoj_levy_filter_remaining_life
            name: Filtr
          - type: tile
            entity: sensor.klimatizace_pokoj_levy_sleep_time
            name: Sleep
      - type: grid
        cards:
          - type: thermostat
            entity: climate.klimatizace_pokoj_stred
            name: Klimatizace Ložnice
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: tile
            entity: switch.klimatizace_pokoj_stred_jet_mode
            vertical: true
            name: Jet Mode
          - type: tile
            entity: switch.klimatizace_pokoj_stred_ionizer
            name: Ionizer
            vertical: true
            tap_action:
              action: toggle
          - type: tile
            entity: sensor.klimatizace_pokoj_stred_filter_remaining_life
            name: Filtr
          - type: tile
            entity: sensor.klimatizace_pokoj_stred_sleep_time
            name: Sleep
      - type: grid
        cards:
          - type: thermostat
            entity: climate.klimatizace_pracovna
            name: Klimatizace Pracovna
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: tile
            entity: switch.klimatizace_pracovna_jet_mode
            name: Jet Mode
            vertical: true
          - type: tile
            entity: switch.klimatizace_pracovna_ionizer
            name: Ionizer
            vertical: true
            tap_action:
              action: toggle
          - type: tile
            entity: sensor.klimatizace_pracovna_filter_remaining_life
            name: Filtr
          - type: tile
            entity: sensor.shelly_klimatizace_switch_0_power
            name: Spotřeba AC
            icon: mdi:flash
      - type: grid
        cards:
          - type: thermostat
            entity: climate.obyvaci_pokoj
            features:
              - type: climate-hvac-modes
            show_current_as_primary: false
            name: Obývací pokoj
          - type: thermostat
            entity: climate.pokoj_dole
            features:
              - type: climate-hvac-modes
            name: Pokoj dole
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.koupelna_dole
            name: Koupelna dole
          - type: tile
            entity: switch.kotel
            name: Hlavní kotel
            icon: mdi:water-boiler
            vertical: true
            tap_action:
              action: toggle
      - type: grid
        cards:
          - type: thermostat
            entity: climate.loznice
            features:
              - type: climate-hvac-modes
            show_current_as_primary: false
            name: Ložnice
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.koupelna_nahore
            name: Koupelna nahoře
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.0x84fd27fffea36224
            name: Chodba
          - type: thermostat
            entity: climate.pokoj_levy
            features:
              - type: climate-hvac-modes
            name: Pokoj levý
      - type: grid
        cards:
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.pokoj_pravy
            name: Pokoj pravý
